name: Firebase Functions CI/CD

on:
  push:
    branches: [ main, develop ]
    paths: [ 'backend/**' ]
  pull_request:
    branches: [ main ]
    paths: [ 'backend/**' ]

env:
  NODE_VERSION: '18'
  FIREBASE_PROJECT_ID: 'bm-app-flutter-01'

jobs:
  lint-and-test:
    name: Lint, Test & Build
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: Install dependencies
      working-directory: backend
      run: npm ci

    - name: Run ESLint
      working-directory: backend
      run: npm run lint

    - name: Run TypeScript compilation
      working-directory: backend
      run: npm run build

    - name: Setup Firebase CLI
      run: npm install -g firebase-tools

    - name: Start Firebase Emulators
      working-directory: backend
      run: |
        firebase emulators:start --only functions,firestore,auth --project ${{ env.FIREBASE_PROJECT_ID }} &
        sleep 10
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

    - name: Run unit tests
      working-directory: backend
      run: npm run test:coverage

    - name: Run integration tests
      working-directory: backend
      run: npm run test:integration
      env:
        FIRESTORE_EMULATOR_HOST: localhost:8080
        FIREBASE_AUTH_EMULATOR_HOST: localhost:9099

    - name: Check test coverage
      working-directory: backend
      run: |
        COVERAGE=$(npm run test:coverage -- --silent | grep -o 'All files.*[0-9]\+' | grep -o '[0-9]\+' | tail -1)
        if [ "$COVERAGE" -lt 90 ]; then
          echo "Test coverage is below 90%: $COVERAGE%"
          exit 1
        fi
        echo "Test coverage: $COVERAGE%"

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: backend/coverage/lcov.info
        flags: backend
        name: backend-coverage

    - name: Stop Firebase Emulators
      if: always()
      run: |
        pkill -f "firebase emulators" || true

  security-check:
    name: Security & IAM Check
    runs-on: ubuntu-latest
    needs: lint-and-test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}

    - name: Install Firebase CLI
      run: npm install -g firebase-tools

    - name: Verify IAM roles
      run: |
        echo "Checking required IAM roles for Cloud Functions..."
        # This would typically check that the service account has minimal required permissions
        # For now, we'll just verify the configuration exists
        if [ -f "backend/package.json" ]; then
          echo "✓ Package configuration found"
        else
          echo "✗ Package configuration missing"
          exit 1
        fi
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

  deploy-preview:
    name: Deploy to Preview Channel
    runs-on: ubuntu-latest
    needs: [lint-and-test, security-check]
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: Install dependencies
      working-directory: backend
      run: npm ci

    - name: Build functions
      working-directory: backend
      run: npm run build

    - name: Install Firebase CLI
      run: npm install -g firebase-tools

    - name: Deploy to preview channel
      working-directory: backend
      run: |
        PREVIEW_CHANNEL="pr-${{ github.event.number }}"
        firebase hosting:channel:deploy $PREVIEW_CHANNEL --project ${{ env.FIREBASE_PROJECT_ID }}
        echo "Preview URL: https://${{ env.FIREBASE_PROJECT_ID }}--$PREVIEW_CHANNEL.web.app"
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [lint-and-test, security-check]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: Install dependencies
      working-directory: backend
      run: npm ci

    - name: Build functions
      working-directory: backend
      run: npm run build

    - name: Install Firebase CLI
      run: npm install -g firebase-tools

    - name: Deploy to production
      working-directory: backend
      run: firebase deploy --only functions --project ${{ env.FIREBASE_PROJECT_ID }}
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

    - name: Verify deployment
      run: |
        echo "✓ Functions deployed successfully to production"
        echo "Region: asia-south1"
        echo "Project: ${{ env.FIREBASE_PROJECT_ID }}"

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: always() && github.ref == 'refs/heads/main'
    
    steps:
    - name: Notify success
      if: needs.deploy-production.result == 'success'
      run: |
        echo "🚀 Firebase Functions deployed successfully!"
        echo "User lifecycle sync functions are now live in production."

    - name: Notify failure
      if: needs.deploy-production.result == 'failure'
      run: |
        echo "❌ Firebase Functions deployment failed!"
        echo "Please check the logs and fix any issues."
        exit 1
