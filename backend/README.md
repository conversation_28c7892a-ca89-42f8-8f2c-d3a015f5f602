# Biomedict Firebase Functions

This directory contains the Firebase Functions for the Biomedict application.

## Setup

The functions are already set up and ready to use. Dependencies are installed and the code is compiled.

## Available Scripts

- `npm run build` - Compile TypeScript to JavaScript
- `npm run build:watch` - Compile TypeScript in watch mode
- `npm run serve` - Start local Firebase emulator
- `npm run deploy` - Deploy functions to Firebase
- `npm run logs` - View function logs
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Run ESLint with auto-fix

## Available Functions

### HTTP Functions

1. **helloWorld** - Simple HTTP endpoint
   - URL: `https://your-project.cloudfunctions.net/helloWorld`
   - Method: GET/POST
   - Returns: "Hello from Firebase Functions!"

### Callable Functions

2. **addMessage** - Add a message to Firestore (requires authentication)
   - Call from client: `firebase.functions().httpsCallable('addMessage')`
   - Parameters: `{ message: string }`
   - Returns: `{ messageId: string }`

### Firestore Triggers

3. **onMessageCreate** - Triggered when a new message is created
   - Automatically runs when a document is added to the `messages` collection
   - Logs the new message details

### Scheduled Functions

4. **dailyCleanup** - Runs daily at midnight UTC
   - Deletes messages older than 30 days
   - Runs automatically via Cloud Scheduler

## Development

### Local Development

1. Start the Firebase emulator:
   ```bash
   npm run serve
   ```

2. The functions will be available at:
   - Functions: http://localhost:5001
   - Firestore: http://localhost:8080
   - Emulator UI: http://localhost:4000

### Deployment

1. Build and deploy:
   ```bash
   npm run deploy
   ```

2. Or deploy from the root directory:
   ```bash
   firebase deploy --only functions
   ```

## Project Structure

```
backend/
├── src/
│   └── index.ts          # Main functions file
├── lib/                  # Compiled JavaScript (auto-generated)
├── node_modules/         # Dependencies
├── .eslintrc.js         # ESLint configuration
├── .gitignore           # Git ignore rules
├── package.json         # Node.js dependencies and scripts
├── tsconfig.json        # TypeScript configuration
└── README.md            # This file
```

## Adding New Functions

1. Add your function to `src/index.ts`
2. Build the project: `npm run build`
3. Test locally: `npm run serve`
4. Deploy: `npm run deploy`

## Environment Variables

To use environment variables in your functions:

1. Set them using Firebase CLI:
   ```bash
   firebase functions:config:set someservice.key="THE API KEY"
   ```

2. Access them in your code:
   ```typescript
   const apiKey = functions.config().someservice.key;
   ```

## Security Rules

Make sure to configure appropriate Firestore security rules for your collections in the Firebase Console.
