# Biomedict Firebase Functions

This directory contains the Firebase Functions for the Biomedict application, implementing automated user-lifecycle sync between Firebase Authentication and Firestore with Clean Architecture principles.

## 🏗️ Architecture

The project follows Clean Architecture principles with clear separation of concerns:

```
backend/src/
├── domain/          # Pure business logic (no external dependencies)
│   ├── UserService.ts       # Core user lifecycle orchestration
│   └── LoggingService.ts    # Structured logging service
├── infra/           # External service adapters
│   ├── FirestoreAdapter.ts  # Firestore operations wrapper
│   └── AuthAdapter.ts       # Auth data extraction utilities
├── functions/       # Thin Cloud Function entry points
│   └── userLifecycle.ts     # Auth trigger handlers
├── types/           # TypeScript type definitions
│   └── User.ts              # User-related interfaces
└── __tests__/       # Comprehensive test suite
    ├── domain/              # Unit tests for business logic
    ├── infra/               # Unit tests for adapters
    └── integration/         # End-to-end integration tests
```

## 🚀 Available Scripts

- `npm run build` - Compile TypeScript to JavaScript
- `npm run build:watch` - Compile TypeScript in watch mode
- `npm run serve` - Start local Firebase emulator with auth, firestore, and functions
- `npm run deploy` - Deploy functions to Firebase
- `npm run logs` - View function logs
- `npm run lint` - Run ESLint with strict rules (--max-warnings 0)
- `npm run lint:fix` - Run ESLint with auto-fix
- `npm run test` - Run unit tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report (requires ≥90%)
- `npm run test:integration` - Run integration tests with emulators
- `npm run clean` - Clean build and coverage directories

## 🔧 Core Functions

### User Lifecycle Functions (Production)

1. **createUserProfile** - Auth trigger for user creation
   - **Trigger**: `auth.user().onCreate`
   - **Action**: Creates user profile document in `users/{uid}`
   - **Region**: `asia-south1`
   - **Data**: Whitelisted auth claims (email, displayName, photoURL, providerData)
   - **Additional**: Creates `userSettings/{uid}` document
   - **Logging**: Structured JSON logs with business analytics events

2. **deleteUserProfile** - Auth trigger for user deletion
   - **Trigger**: `auth.user().onDelete`
   - **Action**: Deletes user profile and all related sub-collections
   - **Region**: `asia-south1`
   - **Idempotent**: Gracefully handles missing documents
   - **Cleanup**: Removes `users/{uid}`, `userSettings/{uid}`, and sub-collections

### Example Functions (Development/Reference)

3. **helloWorld** - Simple HTTP endpoint for testing
4. **addMessage** - Authenticated callable function
5. **onMessageCreate** - Firestore trigger example
6. **dailyCleanup** - Scheduled function example

## Development

### Local Development

1. Start the Firebase emulator:
   ```bash
   npm run serve
   ```

2. The functions will be available at:
   - Functions: http://localhost:5001
   - Firestore: http://localhost:8080
   - Emulator UI: http://localhost:4000

### Deployment

1. Build and deploy:
   ```bash
   npm run deploy
   ```

2. Or deploy from the root directory:
   ```bash
   firebase deploy --only functions
   ```

## Project Structure

```
backend/
├── src/
│   └── index.ts          # Main functions file
├── lib/                  # Compiled JavaScript (auto-generated)
├── node_modules/         # Dependencies
├── .eslintrc.js         # ESLint configuration
├── .gitignore           # Git ignore rules
├── package.json         # Node.js dependencies and scripts
├── tsconfig.json        # TypeScript configuration
└── README.md            # This file
```

## Adding New Functions

1. Add your function to `src/index.ts`
2. Build the project: `npm run build`
3. Test locally: `npm run serve`
4. Deploy: `npm run deploy`

## Environment Variables

To use environment variables in your functions:

1. Set them using Firebase CLI:
   ```bash
   firebase functions:config:set someservice.key="THE API KEY"
   ```

2. Access them in your code:
   ```typescript
   const apiKey = functions.config().someservice.key;
   ```

## Security Rules

Make sure to configure appropriate Firestore security rules for your collections in the Firebase Console.
