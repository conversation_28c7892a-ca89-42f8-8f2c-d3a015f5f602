{"name": "biomedict-functions", "version": "1.0.0", "description": "Firebase Functions for Biomedict application", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0"}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-plugin-import": "^2.25.0", "typescript": "^5.0.0"}, "private": true}