{"name": "biomedict-functions", "version": "1.0.0", "description": "Firebase Functions for Biomedict application", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions,firestore,auth", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "lint": "eslint --ext .js,.ts . --max-warnings 0", "lint:fix": "eslint --ext .js,.ts . --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "npm run build && firebase emulators:exec --only functions,firestore,auth 'npm run test'", "clean": "rm -rf lib coverage"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^5.0.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-plugin-import": "^2.25.0", "firebase-functions-test": "^3.1.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.0"}, "private": true}