/**
 * Firestore adapter for user profile operations
 * Handles all Firestore interactions with proper error handling and batching
 */

import { getFirestore, FieldValue } from 'firebase-admin/firestore';
import { UserProfile, UserOperationResult } from '../types/User';

export class FirestoreAdapter {
  private readonly db = getFirestore();
  private readonly usersCollection = 'users';

  /**
   * Create a user profile document in Firestore
   * Uses batched operations for consistency
   */
  async createUserProfile(
    userProfile: UserProfile,
    additionalCollections: string[] = []
  ): Promise<UserOperationResult> {
    try {
      const batch = this.db.batch();
      
      // Create main user document
      const userDocRef = this.db.collection(this.usersCollection).doc(userProfile.uid);
      batch.set(userDocRef, {
        ...userProfile,
        createdAt: FieldValue.serverTimestamp(),
        updatedAt: FieldValue.serverTimestamp(),
      });

      // Create additional collection documents if specified
      for (const collectionName of additionalCollections) {
        const additionalDocRef = this.db
          .collection(collectionName)
          .doc(userProfile.uid);
        
        batch.set(additionalDocRef, {
          uid: userProfile.uid,
          createdAt: FieldValue.serverTimestamp(),
          updatedAt: FieldValue.serverTimestamp(),
        });
      }

      await batch.commit();

      return {
        success: true,
        uid: userProfile.uid,
        metadata: {
          collectionsCreated: [this.usersCollection, ...additionalCollections],
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        uid: userProfile.uid,
        error: error as Error,
        metadata: {
          operation: 'createUserProfile',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Delete a user profile document and all related sub-collections
   * Uses batched operations for consistency
   */
  async deleteUserProfile(
    uid: string,
    additionalCollections: string[] = []
  ): Promise<UserOperationResult> {
    try {
      const batch = this.db.batch();
      
      // Check if user document exists
      const userDocRef = this.db.collection(this.usersCollection).doc(uid);
      const userDoc = await userDocRef.get();
      
      if (!userDoc.exists) {
        // Document doesn't exist - this is not an error for idempotent deletes
        return {
          success: true,
          uid,
          metadata: {
            documentExists: false,
            message: 'User document does not exist, deletion is idempotent',
            timestamp: new Date().toISOString(),
          },
        };
      }

      // Delete main user document
      batch.delete(userDocRef);

      // Delete sub-collections (e.g., userSettings, userPreferences)
      const subCollections = await userDocRef.listCollections();
      for (const subCollection of subCollections) {
        const subDocs = await subCollection.get();
        subDocs.docs.forEach(doc => {
          batch.delete(doc.ref);
        });
      }

      // Delete additional collection documents if specified
      for (const collectionName of additionalCollections) {
        const additionalDocRef = this.db
          .collection(collectionName)
          .doc(uid);
        
        batch.delete(additionalDocRef);
      }

      await batch.commit();

      return {
        success: true,
        uid,
        metadata: {
          documentExists: true,
          collectionsDeleted: [this.usersCollection, ...additionalCollections],
          subCollectionsDeleted: subCollections.length,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        uid,
        error: error as Error,
        metadata: {
          operation: 'deleteUserProfile',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Check if a user profile document exists
   */
  async userProfileExists(uid: string): Promise<boolean> {
    try {
      const userDocRef = this.db.collection(this.usersCollection).doc(uid);
      const userDoc = await userDocRef.get();
      return userDoc.exists;
    } catch (error) {
      // If we can't check, assume it doesn't exist
      return false;
    }
  }

  /**
   * Get user profile document
   */
  async getUserProfile(uid: string): Promise<UserProfile | null> {
    try {
      const userDocRef = this.db.collection(this.usersCollection).doc(uid);
      const userDoc = await userDocRef.get();
      
      if (!userDoc.exists) {
        return null;
      }
      
      return userDoc.data() as UserProfile;
    } catch (error) {
      return null;
    }
  }
}
