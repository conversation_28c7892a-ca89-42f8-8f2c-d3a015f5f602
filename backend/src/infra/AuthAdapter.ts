/**
 * Auth adapter for extracting and transforming Firebase Auth user data
 * Handles whitelisting of auth claims and data transformation
 */

import { UserRecord } from 'firebase-admin/auth';
import { UserProfile, UserProviderData, AuthUserContext } from '../types/User';

export class AuthAdapter {
  /**
   * Extract whitelisted user profile data from Firebase Auth UserRecord
   */
  extractUserProfile(
    userRecord: UserRecord,
    includeCustomClaims: boolean = false,
    includeProviderData: boolean = true
  ): UserProfile {
    const providerData: UserProviderData[] = includeProviderData
      ? userRecord.providerData.map(provider => ({
        uid: provider.uid,
        providerId: provider.providerId,
        email: provider.email,
        displayName: provider.displayName,
        photoURL: provider.photoURL,
      }))
      : [];

    const userProfile: UserProfile = {
      uid: userRecord.uid,
      email: userRecord.email,
      displayName: userRecord.displayName,
      photoURL: userRecord.photoURL,
      emailVerified: userRecord.emailVerified,
      disabled: userRecord.disabled,
      providerData,
      createdAt: userRecord.metadata.creationTime,
      lastSignInAt: userRecord.metadata.lastSignInTime,
    };

    // Include custom claims if requested and they exist
    if (includeCustomClaims && userRecord.customClaims) {
      userProfile.customClaims = userRecord.customClaims;
    }

    return userProfile;
  }

  /**
   * Get the primary authentication provider for a user
   */
  getPrimaryProvider(userRecord: UserRecord): string {
    // If user has provider data, use the first provider
    if (userRecord.providerData && userRecord.providerData.length > 0) {
      return userRecord.providerData[0].providerId;
    }
    
    // Fallback to determining provider based on available data
    if (userRecord.email) {
      return 'password'; // Email/password authentication
    }
    
    return 'unknown';
  }

  /**
   * Create auth user context with additional metadata
   */
  createAuthUserContext(
    userRecord: UserRecord,
    eventId?: string
  ): AuthUserContext {
    return {
      user: userRecord,
      eventId,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Validate user record has required fields for profile creation
   */
  validateUserRecord(userRecord: UserRecord): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!userRecord.uid) {
      errors.push('User UID is required');
    }

    if (userRecord.uid && userRecord.uid.length < 1) {
      errors.push('User UID cannot be empty');
    }

    // Email is not strictly required as users can sign in with phone, etc.
    // but we should validate format if present
    if (userRecord.email && !this.isValidEmail(userRecord.email)) {
      errors.push('Invalid email format');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Simple email validation
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Check if user record indicates a test/development user
   * Useful for filtering out test users in production
   */
  isTestUser(userRecord: UserRecord): boolean {
    const testDomains = ['test.com', 'example.com', 'localhost'];
    const testEmailPatterns = [/test\+/, /dev\+/, /staging\+/];

    if (userRecord.email) {
      const email = userRecord.email.toLowerCase();
      
      // Check test domains
      for (const domain of testDomains) {
        if (email.endsWith(`@${domain}`)) {
          return true;
        }
      }

      // Check test email patterns
      for (const pattern of testEmailPatterns) {
        if (pattern.test(email)) {
          return true;
        }
      }
    }

    // Check if displayName indicates test user
    if (userRecord.displayName) {
      const displayName = userRecord.displayName.toLowerCase();
      if (displayName.includes('test') || displayName.includes('dev')) {
        return true;
      }
    }

    return false;
  }

  /**
   * Extract metadata for logging purposes
   */
  extractLoggingMetadata(userRecord: UserRecord): Record<string, any> {
    return {
      uid: userRecord.uid,
      email: userRecord.email,
      emailVerified: userRecord.emailVerified,
      disabled: userRecord.disabled,
      providerCount: userRecord.providerData.length,
      primaryProvider: this.getPrimaryProvider(userRecord),
      hasCustomClaims: !!userRecord.customClaims && Object.keys(userRecord.customClaims).length > 0,
      creationTime: userRecord.metadata.creationTime,
      lastSignInTime: userRecord.metadata.lastSignInTime,
    };
  }
}
