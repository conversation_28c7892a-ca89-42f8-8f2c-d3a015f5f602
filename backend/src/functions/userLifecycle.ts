/**
 * Firebase Cloud Functions for user lifecycle management
 * Thin trigger handlers that delegate to domain services
 */

import * as functions from 'firebase-functions';
import { UserService } from '../domain/UserService';
import { FirestoreAdapter } from '../infra/FirestoreAdapter';
import { AuthAdapter } from '../infra/AuthAdapter';
import { LoggingService } from '../domain/LoggingService';
import { UserProfileConfig } from '../types/User';

// Configure regional deployment for auth triggers (v1 functions)
const authFunctions = functions.region('asia-south1');

// Initialize services (dependency injection)
const firestoreAdapter = new FirestoreAdapter();
const authAdapter = new AuthAdapter();
const loggingService = new LoggingService();
const userService = new UserService(firestoreAdapter, authAdapter, loggingService);

// Configuration for user profile creation
const userProfileConfig: UserProfileConfig = {
  includeCustomClaims: false, // Don't include custom claims by default for security
  includeProviderData: true,  // Include provider data for analytics
  additionalCollections: ['userSettings'], // Create userSettings collection
};

/**
 * Triggered when a new user is created in Firebase Auth
 * Creates a corresponding user profile document in Firestore
 */
export const createUserProfile = authFunctions.auth.user().onCreate(async (user, context) => {
  try {
    // Log function invocation
    loggingService.logDebug(
      'user_profile_created',
      user.uid,
      'User creation trigger invoked',
      {
        eventId: context.eventId,
        eventType: context.eventType,
        source: 'firebase-auth',
        timestamp: context.timestamp,
      }
    );

    // Delegate to domain service
    const result = await userService.handleUserCreated(user, userProfileConfig);

    if (!result.success) {
      // Error is already logged by the service, but we should throw to mark function as failed
      throw result.error || new Error('User profile creation failed');
    }

    // Function completed successfully
    loggingService.logDebug(
      'user_profile_created',
      user.uid,
      'User creation trigger completed successfully',
      {
        eventId: context.eventId,
        executionTime: Date.now() - new Date(context.timestamp).getTime(),
      }
    );

    return null;
  } catch (error) {
    // Log unexpected errors and re-throw to mark function as failed
    loggingService.logUserProfileCreationFailed(
      user.uid,
      error as Error,
      authAdapter.getPrimaryProvider(user),
      {
        eventId: context.eventId,
        functionName: 'createUserProfile',
        triggerType: 'auth.user.onCreate',
      }
    );

    throw error; // Re-throw to mark function execution as failed
  }
});

/**
 * Triggered when a user is deleted from Firebase Auth
 * Removes the corresponding user profile document from Firestore
 */
export const deleteUserProfile = authFunctions.auth.user().onDelete(async (user, context) => {
  const uid = user.uid;

  try {
    // Log function invocation
    loggingService.logDebug(
      'user_profile_deleted',
      uid,
      'User deletion trigger invoked',
      {
        eventId: context.eventId,
        eventType: context.eventType,
        source: 'firebase-auth',
        timestamp: context.timestamp,
      }
    );

    // Delegate to domain service
    // Include userSettings in deletion to clean up all related data
    const result = await userService.handleUserDeleted(uid, ['userSettings']);

    if (!result.success) {
      // Error is already logged by the service, but we should throw to mark function as failed
      throw result.error || new Error('User profile deletion failed');
    }

    // Function completed successfully
    loggingService.logDebug(
      'user_profile_deleted',
      uid,
      'User deletion trigger completed successfully',
      {
        eventId: context.eventId,
        executionTime: Date.now() - new Date(context.timestamp).getTime(),
      }
    );

    return null;
  } catch (error) {
    // Log unexpected errors and re-throw to mark function as failed
    loggingService.logUserProfileDeletionFailed(
      uid,
      error as Error,
      {
        eventId: context.eventId,
        functionName: 'deleteUserProfile',
        triggerType: 'auth.user.onDelete',
      }
    );

    throw error; // Re-throw to mark function execution as failed
  }
});

// Export services for testing
export { userService, firestoreAdapter, authAdapter, loggingService };
