/**
 * Firebase Cloud Functions entry point
 * Exports all functions and initializes Firebase Admin SDK
 */

import * as admin from "firebase-admin";

// Initialize Firebase Admin SDK
admin.initializeApp();

// Export user lifecycle functions
export {
  createUserProfile,
  deleteUserProfile,
} from "./functions/userLifecycle";

// Keep existing example functions for reference (can be removed in production)
import * as functions from "firebase-functions";

// Example HTTP function
export const helloWorld = functions.https.onRequest((request, response) => {
  functions.logger.info("Hello logs!", {structuredData: true});
  response.send("Hello from Firebase Functions!");
});

// Example callable function
export const addMessage = functions.https.onCall(async (data, context) => {
  // Verify user authentication
  if (!context.auth) {
    throw new functions.https.HttpsError(
      "unauthenticated",
      "The function must be called while authenticated."
    );
  }

  const message = data.message;
  if (!message) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "The function must be called with a message."
    );
  }

  try {
    // Add message to Firestore
    const writeResult = await admin.firestore()
      .collection("messages")
      .add({
        message: message,
        userId: context.auth.uid,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
      });

    functions.logger.info("Message added", {messageId: writeResult.id});
    return {messageId: writeResult.id};
  } catch (error) {
    functions.logger.error("Error adding message", error);
    throw new functions.https.HttpsError(
      "internal",
      "Unable to add message"
    );
  }
});

// Example Firestore trigger
export const onMessageCreate = functions.firestore
  .document("messages/{messageId}")
  .onCreate((snap, context) => {
    const newValue = snap.data();
    functions.logger.info("New message created", {
      messageId: context.params.messageId,
      message: newValue.message,
    });

    // You can add additional logic here, such as:
    // - Sending notifications
    // - Updating other documents
    // - Calling external APIs

    return null;
  });

// Example scheduled function (runs every day at midnight)
export const dailyCleanup = functions.pubsub
  .schedule("0 0 * * *")
  .timeZone("UTC")
  .onRun(async (_context) => {
    functions.logger.info("Running daily cleanup");

    // Example: Delete messages older than 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const oldMessages = await admin.firestore()
      .collection("messages")
      .where("timestamp", "<", thirtyDaysAgo)
      .get();

    const batch = admin.firestore().batch();
    oldMessages.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    functions.logger.info(`Deleted ${oldMessages.size} old messages`);

    return null;
  });
