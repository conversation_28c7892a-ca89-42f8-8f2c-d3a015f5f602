/**
 * User-related type definitions for Firebase Auth and Firestore integration
 */

import { UserRecord } from 'firebase-admin/auth';

/**
 * Whitelisted Firebase Auth user claims that can be stored in Firestore
 */
export interface UserProfile {
  uid: string;
  email?: string;
  displayName?: string;
  photoURL?: string;
  emailVerified: boolean;
  disabled: boolean;
  providerData: UserProviderData[];
  createdAt: string;
  lastSignInAt?: string;
  customClaims?: Record<string, any>;
}

/**
 * Provider-specific user data
 */
export interface UserProviderData {
  uid: string;
  providerId: string;
  email?: string;
  displayName?: string;
  photoURL?: string;
}

/**
 * Structured logging event types
 */
export type LogEvent = 
  | 'user_profile_created'
  | 'user_profile_deleted'
  | 'user_profile_creation_failed'
  | 'user_profile_deletion_failed';

/**
 * Structured log entry interface
 */
export interface LogEntry {
  severity: 'DEBUG' | 'INFO' | 'NOTICE' | 'WARNING' | 'ERROR' | 'CRITICAL';
  event: LogEvent;
  uid: string;
  provider?: string;
  timestamp: string;
  message?: string;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Firebase Auth user record with additional metadata
 */
export interface AuthUserContext {
  user: UserRecord;
  eventId?: string;
  timestamp: string;
}

/**
 * Result of user profile operations
 */
export interface UserOperationResult {
  success: boolean;
  uid: string;
  error?: Error;
  metadata?: Record<string, any>;
}

/**
 * Configuration for user profile creation
 */
export interface UserProfileConfig {
  includeCustomClaims: boolean;
  includeProviderData: boolean;
  additionalCollections?: string[];
}
