/**
 * Unit tests for UserService
 */

import { UserService } from '../../domain/UserService';
import { FirestoreAdapter } from '../../infra/FirestoreAdapter';
import { AuthAdapter } from '../../infra/AuthAdapter';
import { LoggingService } from '../../domain/LoggingService';
import { UserOperationResult } from '../../types/User';

// Mock the dependencies
jest.mock('../../infra/FirestoreAdapter');
jest.mock('../../infra/AuthAdapter');
jest.mock('../../domain/LoggingService');

describe('UserService', () => {
  let userService: UserService;
  let mockFirestoreAdapter: jest.Mocked<FirestoreAdapter>;
  let mockAuthAdapter: jest.Mocked<AuthAdapter>;
  let mockLoggingService: jest.Mocked<LoggingService>;

  beforeEach(() => {
    mockFirestoreAdapter = new FirestoreAdapter() as jest.Mocked<FirestoreAdapter>;
    mockAuthAdapter = new AuthAdapter() as jest.Mocked<AuthAdapter>;
    mockLoggingService = new LoggingService() as jest.Mocked<LoggingService>;
    
    userService = new UserService(
      mockFirestoreAdapter,
      mockAuthAdapter,
      mockLoggingService
    );
  });

  describe('handleUserCreated', () => {
    const mockUserRecord = global.testUtils.createMockUserRecord();

    it('should successfully create user profile', async () => {
      // Arrange
      const expectedUserProfile = {
        uid: mockUserRecord.uid,
        email: mockUserRecord.email,
        displayName: mockUserRecord.displayName,
        photoURL: mockUserRecord.photoURL,
        emailVerified: mockUserRecord.emailVerified,
        disabled: mockUserRecord.disabled,
        providerData: mockUserRecord.providerData,
        createdAt: mockUserRecord.metadata.creationTime,
        lastSignInAt: mockUserRecord.metadata.lastSignInTime,
      };

      const expectedResult: UserOperationResult = {
        success: true,
        uid: mockUserRecord.uid,
        metadata: { collectionsCreated: ['users'] },
      };

      mockAuthAdapter.validateUserRecord.mockReturnValue({
        isValid: true,
        errors: [],
      });
      mockAuthAdapter.getPrimaryProvider.mockReturnValue('password');
      mockAuthAdapter.extractUserProfile.mockReturnValue(expectedUserProfile);
      mockAuthAdapter.isTestUser.mockReturnValue(false);
      mockAuthAdapter.extractLoggingMetadata.mockReturnValue({});
      mockFirestoreAdapter.createUserProfile.mockResolvedValue(expectedResult);

      // Act
      const result = await userService.handleUserCreated(mockUserRecord);

      // Assert
      expect(result.success).toBe(true);
      expect(result.uid).toBe(mockUserRecord.uid);
      expect(mockAuthAdapter.validateUserRecord).toHaveBeenCalledWith(mockUserRecord);
      expect(mockAuthAdapter.extractUserProfile).toHaveBeenCalledWith(
        mockUserRecord,
        false,
        true
      );
      expect(mockFirestoreAdapter.createUserProfile).toHaveBeenCalledWith(
        expectedUserProfile,
        []
      );
      expect(mockLoggingService.logUserProfileCreated).toHaveBeenCalledWith(
        mockUserRecord.uid,
        'password',
        expect.any(Object)
      );
    });

    it('should handle validation errors', async () => {
      // Arrange
      const validationErrors = ['User UID is required'];
      mockAuthAdapter.validateUserRecord.mockReturnValue({
        isValid: false,
        errors: validationErrors,
      });
      mockAuthAdapter.getPrimaryProvider.mockReturnValue('password');

      // Act
      const result = await userService.handleUserCreated(mockUserRecord);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain('Invalid user record');
      expect(mockLoggingService.logUserProfileCreationFailed).toHaveBeenCalledWith(
        mockUserRecord.uid,
        expect.any(Error),
        'password',
        { validationErrors }
      );
    });

    it('should handle Firestore creation failure', async () => {
      // Arrange
      const firestoreError = new Error('Firestore connection failed');
      const expectedResult: UserOperationResult = {
        success: false,
        uid: mockUserRecord.uid,
        error: firestoreError,
      };

      mockAuthAdapter.validateUserRecord.mockReturnValue({
        isValid: true,
        errors: [],
      });
      mockAuthAdapter.getPrimaryProvider.mockReturnValue('password');
      mockAuthAdapter.extractUserProfile.mockReturnValue({} as any);
      mockAuthAdapter.isTestUser.mockReturnValue(false);
      mockFirestoreAdapter.createUserProfile.mockResolvedValue(expectedResult);

      // Act
      const result = await userService.handleUserCreated(mockUserRecord);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe(firestoreError);
      expect(mockLoggingService.logUserProfileCreationFailed).toHaveBeenCalledWith(
        mockUserRecord.uid,
        firestoreError,
        'password',
        expect.any(Object)
      );
    });

    it('should handle unexpected errors', async () => {
      // Arrange
      const unexpectedError = new Error('Unexpected error');
      mockAuthAdapter.validateUserRecord.mockImplementation(() => {
        throw unexpectedError;
      });
      mockAuthAdapter.getPrimaryProvider.mockReturnValue('password');

      // Act
      const result = await userService.handleUserCreated(mockUserRecord);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe(unexpectedError);
      expect(mockLoggingService.logUserProfileCreationFailed).toHaveBeenCalledWith(
        mockUserRecord.uid,
        unexpectedError,
        'password',
        { unexpectedError: true }
      );
    });
  });

  describe('handleUserDeleted', () => {
    const testUid = 'test-uid-123';

    it('should successfully delete user profile', async () => {
      // Arrange
      const expectedResult: UserOperationResult = {
        success: true,
        uid: testUid,
        metadata: { documentExists: true, collectionsDeleted: ['users'] },
      };

      mockFirestoreAdapter.userProfileExists.mockResolvedValue(true);
      mockFirestoreAdapter.deleteUserProfile.mockResolvedValue(expectedResult);

      // Act
      const result = await userService.handleUserDeleted(testUid);

      // Assert
      expect(result.success).toBe(true);
      expect(result.uid).toBe(testUid);
      expect(mockFirestoreAdapter.deleteUserProfile).toHaveBeenCalledWith(testUid, []);
      expect(mockLoggingService.logUserProfileDeleted).toHaveBeenCalledWith(
        testUid,
        expectedResult.metadata
      );
    });

    it('should handle missing user document gracefully', async () => {
      // Arrange
      const expectedResult: UserOperationResult = {
        success: true,
        uid: testUid,
        metadata: { documentExists: false },
      };

      mockFirestoreAdapter.userProfileExists.mockResolvedValue(false);
      mockFirestoreAdapter.deleteUserProfile.mockResolvedValue(expectedResult);

      // Act
      const result = await userService.handleUserDeleted(testUid);

      // Assert
      expect(result.success).toBe(true);
      expect(mockLoggingService.logUserDocumentNotFound).toHaveBeenCalledWith(testUid);
      expect(mockLoggingService.logUserProfileDeleted).toHaveBeenCalledWith(
        testUid,
        expectedResult.metadata
      );
    });

    it('should handle deletion failure', async () => {
      // Arrange
      const deletionError = new Error('Deletion failed');
      const expectedResult: UserOperationResult = {
        success: false,
        uid: testUid,
        error: deletionError,
      };

      mockFirestoreAdapter.userProfileExists.mockResolvedValue(true);
      mockFirestoreAdapter.deleteUserProfile.mockResolvedValue(expectedResult);

      // Act
      const result = await userService.handleUserDeleted(testUid);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe(deletionError);
      expect(mockLoggingService.logUserProfileDeletionFailed).toHaveBeenCalledWith(
        testUid,
        deletionError,
        expectedResult.metadata
      );
    });
  });

  describe('validateUserProfileSync', () => {
    const testUid = 'test-uid-123';

    it('should return sync status when profiles are in sync', async () => {
      // Arrange
      mockFirestoreAdapter.userProfileExists.mockResolvedValue(true);

      // Act
      const result = await userService.validateUserProfileSync(testUid);

      // Assert
      expect(result.isSync).toBe(true);
      expect(result.authExists).toBe(true);
      expect(result.firestoreExists).toBe(true);
      expect(result.issues).toHaveLength(0);
    });

    it('should detect when Firestore profile is missing', async () => {
      // Arrange
      mockFirestoreAdapter.userProfileExists.mockResolvedValue(false);

      // Act
      const result = await userService.validateUserProfileSync(testUid);

      // Assert
      expect(result.isSync).toBe(false);
      expect(result.authExists).toBe(true);
      expect(result.firestoreExists).toBe(false);
      expect(result.issues).toContain('Auth user exists but Firestore profile is missing');
    });
  });
});
