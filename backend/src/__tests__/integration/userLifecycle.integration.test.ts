/**
 * Integration tests for user lifecycle functions
 * Tests the complete flow using Firebase emulators
 */

import * as admin from 'firebase-admin';
import { UserService } from '../../domain/UserService';
import { FirestoreAdapter } from '../../infra/FirestoreAdapter';
import { AuthAdapter } from '../../infra/AuthAdapter';
import { LoggingService } from '../../domain/LoggingService';

describe('User Lifecycle Integration Tests', () => {
  let userService: UserService;
  let firestoreAdapter: FirestoreAdapter;
  let authAdapter: AuthAdapter;
  let loggingService: LoggingService;
  let db: admin.firestore.Firestore;

  beforeAll(() => {
    // Initialize services
    firestoreAdapter = new FirestoreAdapter();
    authAdapter = new AuthAdapter();
    loggingService = new LoggingService();
    userService = new UserService(firestoreAdapter, authAdapter, loggingService);
    db = admin.firestore();
  });

  describe('User Creation Flow', () => {
    it('should create user profile in Firestore when user is created in Auth', async () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord({
        uid: 'integration-test-user-1',
        email: '<EMAIL>',
        displayName: 'Integration Test User 1',
      });

      // Act
      const result = await userService.handleUserCreated(mockUserRecord, {
        includeCustomClaims: false,
        includeProviderData: true,
        additionalCollections: ['userSettings'],
      });

      // Assert
      expect(result.success).toBe(true);
      expect(result.uid).toBe(mockUserRecord.uid);

      // Verify user document was created in Firestore
      const userDoc = await db.collection('users').doc(mockUserRecord.uid).get();
      expect(userDoc.exists).toBe(true);
      
      const userData = userDoc.data();
      expect(userData?.uid).toBe(mockUserRecord.uid);
      expect(userData?.email).toBe(mockUserRecord.email);
      expect(userData?.displayName).toBe(mockUserRecord.displayName);
      expect(userData?.emailVerified).toBe(mockUserRecord.emailVerified);
      expect(userData?.disabled).toBe(mockUserRecord.disabled);
      expect(userData?.providerData).toEqual(mockUserRecord.providerData);

      // Verify additional collection was created
      const userSettingsDoc = await db.collection('userSettings').doc(mockUserRecord.uid).get();
      expect(userSettingsDoc.exists).toBe(true);
      
      const settingsData = userSettingsDoc.data();
      expect(settingsData?.uid).toBe(mockUserRecord.uid);
      expect(settingsData?.createdAt).toBeDefined();
    });

    it('should handle user creation with custom claims', async () => {
      // Arrange
      const customClaims = { role: 'premium', tier: 'gold' };
      const mockUserRecord = global.testUtils.createMockUserRecord({
        uid: 'integration-test-user-2',
        email: '<EMAIL>',
        customClaims,
      });

      // Act
      const result = await userService.handleUserCreated(mockUserRecord, {
        includeCustomClaims: true,
        includeProviderData: true,
        additionalCollections: [],
      });

      // Assert
      expect(result.success).toBe(true);

      // Verify custom claims were stored
      const userDoc = await db.collection('users').doc(mockUserRecord.uid).get();
      const userData = userDoc.data();
      expect(userData?.customClaims).toEqual(customClaims);
    });

    it('should handle user creation failure gracefully', async () => {
      // Arrange - Create a user record with invalid data
      const invalidUserRecord = global.testUtils.createMockUserRecord({
        uid: '', // Invalid empty UID
        email: 'invalid-email', // Invalid email format
      });

      // Act
      const result = await userService.handleUserCreated(invalidUserRecord);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain('Invalid user record');

      // Verify no document was created
      const userDoc = await db.collection('users').doc('').get();
      expect(userDoc.exists).toBe(false);
    });
  });

  describe('User Deletion Flow', () => {
    beforeEach(async () => {
      // Create a test user document to delete
      const testUid = 'integration-test-delete-user';
      await db.collection('users').doc(testUid).set({
        uid: testUid,
        email: '<EMAIL>',
        displayName: 'Delete Test User',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      // Create additional collections
      await db.collection('userSettings').doc(testUid).set({
        uid: testUid,
        theme: 'dark',
        notifications: true,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      // Create a sub-collection
      await db.collection('users').doc(testUid).collection('preferences').doc('general').set({
        language: 'en',
        timezone: 'UTC',
      });
    });

    it('should delete user profile and related data when user is deleted from Auth', async () => {
      // Arrange
      const testUid = 'integration-test-delete-user';

      // Verify documents exist before deletion
      const userDocBefore = await db.collection('users').doc(testUid).get();
      const settingsDocBefore = await db.collection('userSettings').doc(testUid).get();
      expect(userDocBefore.exists).toBe(true);
      expect(settingsDocBefore.exists).toBe(true);

      // Act
      const result = await userService.handleUserDeleted(testUid, ['userSettings']);

      // Assert
      expect(result.success).toBe(true);
      expect(result.uid).toBe(testUid);

      // Verify main user document was deleted
      const userDocAfter = await db.collection('users').doc(testUid).get();
      expect(userDocAfter.exists).toBe(false);

      // Verify additional collection document was deleted
      const settingsDocAfter = await db.collection('userSettings').doc(testUid).get();
      expect(settingsDocAfter.exists).toBe(false);

      // Verify sub-collections were deleted
      const preferencesSnapshot = await db.collection('users').doc(testUid).collection('preferences').get();
      expect(preferencesSnapshot.empty).toBe(true);
    });

    it('should handle deletion of non-existent user gracefully', async () => {
      // Arrange
      const nonExistentUid = 'non-existent-user-123';

      // Act
      const result = await userService.handleUserDeleted(nonExistentUid);

      // Assert
      expect(result.success).toBe(true);
      expect(result.uid).toBe(nonExistentUid);
      expect(result.metadata?.documentExists).toBe(false);
    });
  });

  describe('User Profile Synchronization', () => {
    it('should validate sync status correctly', async () => {
      // Arrange - Create a user in Firestore
      const testUid = 'sync-test-user';
      await db.collection('users').doc(testUid).set({
        uid: testUid,
        email: '<EMAIL>',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      // Act
      const syncStatus = await userService.validateUserProfileSync(testUid);

      // Assert
      expect(syncStatus.isSync).toBe(true);
      expect(syncStatus.authExists).toBe(true);
      expect(syncStatus.firestoreExists).toBe(true);
      expect(syncStatus.issues).toHaveLength(0);
    });

    it('should detect missing Firestore profile', async () => {
      // Arrange
      const testUid = 'missing-firestore-user';
      // Don't create Firestore document

      // Act
      const syncStatus = await userService.validateUserProfileSync(testUid);

      // Assert
      expect(syncStatus.isSync).toBe(false);
      expect(syncStatus.authExists).toBe(true);
      expect(syncStatus.firestoreExists).toBe(false);
      expect(syncStatus.issues).toContain('Auth user exists but Firestore profile is missing');
    });
  });

  describe('Performance Tests', () => {
    it('should create user profile within acceptable time limits', async () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord({
        uid: 'performance-test-user',
        email: '<EMAIL>',
      });

      const startTime = Date.now();

      // Act
      const result = await userService.handleUserCreated(mockUserRecord);

      // Assert
      const executionTime = Date.now() - startTime;
      expect(result.success).toBe(true);
      expect(executionTime).toBeLessThan(500); // Should complete within 500ms in emulator
    });

    it('should delete user profile within acceptable time limits', async () => {
      // Arrange
      const testUid = 'performance-delete-user';
      await db.collection('users').doc(testUid).set({
        uid: testUid,
        email: '<EMAIL>',
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      const startTime = Date.now();

      // Act
      const result = await userService.handleUserDeleted(testUid);

      // Assert
      const executionTime = Date.now() - startTime;
      expect(result.success).toBe(true);
      expect(executionTime).toBeLessThan(500); // Should complete within 500ms in emulator
    });
  });
});
