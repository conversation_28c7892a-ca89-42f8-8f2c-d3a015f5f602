/**
 * Simple test to verify Jest setup
 */

describe('Simple Test', () => {
  it('should pass a basic test', () => {
    expect(1 + 1).toBe(2);
  });

  it('should test global testUtils', () => {
    expect(global.testUtils).toBeDefined();
    expect(global.testUtils.createMockUserRecord).toBeDefined();
    
    const mockUser = global.testUtils.createMockUserRecord();
    expect(mockUser.uid).toBe('test-uid-123');
    expect(mockUser.email).toBe('<EMAIL>');
  });
});
