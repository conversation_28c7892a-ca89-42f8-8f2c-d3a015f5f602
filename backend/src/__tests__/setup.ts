/**
 * Jest test setup file
 * Configures Firebase emulators and test environment
 */

import * as admin from 'firebase-admin';

// Initialize Firebase Admin SDK for testing
if (!admin.apps.length) {
  admin.initializeApp({
    projectId: 'test-project',
  });
}

// Set Firestore emulator settings
process.env.FIRESTORE_EMULATOR_HOST = 'localhost:8080';
process.env.FIREBASE_AUTH_EMULATOR_HOST = 'localhost:9099';

// Global test timeout
jest.setTimeout(10000);

// Clean up after each test
afterEach(async () => {
  // Clear Firestore data
  const db = admin.firestore();
  const collections = await db.listCollections();
  
  const deletePromises = collections.map(async (collection) => {
    const docs = await collection.get();
    const batch = db.batch();
    docs.docs.forEach(doc => batch.delete(doc.ref));
    if (docs.size > 0) {
      await batch.commit();
    }
  });
  
  await Promise.all(deletePromises);
});

// Global test utilities
global.testUtils = {
  createMockUserRecord: (overrides: any = {}) => ({
    uid: 'test-uid-123',
    email: '<EMAIL>',
    displayName: 'Test User',
    photoURL: 'https://example.com/photo.jpg',
    emailVerified: true,
    disabled: false,
    metadata: {
      creationTime: '2023-01-01T00:00:00.000Z',
      lastSignInTime: '2023-01-01T00:00:00.000Z',
    },
    providerData: [
      {
        uid: 'test-uid-123',
        providerId: 'password',
        email: '<EMAIL>',
        displayName: 'Test User',
        photoURL: 'https://example.com/photo.jpg',
      },
    ],
    customClaims: {},
    ...overrides,
  }),
};

// Extend global types
declare global {
  // eslint-disable-next-line no-var
  var testUtils: {
    createMockUserRecord: (overrides?: any) => any;
  };
}
