/**
 * Unit tests for AuthAdapter
 */

import { AuthAdapter } from '../../infra/AuthAdapter';

describe('AuthAdapter', () => {
  let authAdapter: AuthAdapter;

  beforeEach(() => {
    authAdapter = new AuthAdapter();
  });

  describe('extractUserProfile', () => {
    it('should extract basic user profile data', () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord();

      // Act
      const userProfile = authAdapter.extractUserProfile(mockUserRecord);

      // Assert
      expect(userProfile).toEqual({
        uid: mockUserRecord.uid,
        email: mockUserRecord.email,
        displayName: mockUserRecord.displayName,
        photoURL: mockUserRecord.photoURL,
        emailVerified: mockUserRecord.emailVerified,
        disabled: mockUserRecord.disabled,
        providerData: mockUserRecord.providerData,
        createdAt: mockUserRecord.metadata.creationTime,
        lastSignInAt: mockUserRecord.metadata.lastSignInTime,
      });
    });

    it('should include custom claims when requested', () => {
      // Arrange
      const customClaims = { role: 'admin', premium: true };
      const mockUserRecord = global.testUtils.createMockUserRecord({
        customClaims,
      });

      // Act
      const userProfile = authAdapter.extractUserProfile(
        mockUserRecord,
        true, // includeCustomClaims
        true
      );

      // Assert
      expect(userProfile.customClaims).toEqual(customClaims);
    });

    it('should exclude provider data when requested', () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord();

      // Act
      const userProfile = authAdapter.extractUserProfile(
        mockUserRecord,
        false,
        false // includeProviderData
      );

      // Assert
      expect(userProfile.providerData).toEqual([]);
    });

    it('should handle missing optional fields', () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord({
        email: undefined,
        displayName: undefined,
        photoURL: undefined,
        metadata: {
          creationTime: '2023-01-01T00:00:00.000Z',
          lastSignInTime: undefined,
        },
      });

      // Act
      const userProfile = authAdapter.extractUserProfile(mockUserRecord);

      // Assert
      expect(userProfile.email).toBeUndefined();
      expect(userProfile.displayName).toBeUndefined();
      expect(userProfile.photoURL).toBeUndefined();
      expect(userProfile.lastSignInAt).toBeUndefined();
    });
  });

  describe('getPrimaryProvider', () => {
    it('should return first provider from providerData', () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord({
        providerData: [
          { providerId: 'google.com', uid: 'google-123' },
          { providerId: 'facebook.com', uid: 'fb-123' },
        ],
      });

      // Act
      const provider = authAdapter.getPrimaryProvider(mockUserRecord);

      // Assert
      expect(provider).toBe('google.com');
    });

    it('should return "password" for email users with no provider data', () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord({
        email: '<EMAIL>',
        providerData: [],
      });

      // Act
      const provider = authAdapter.getPrimaryProvider(mockUserRecord);

      // Assert
      expect(provider).toBe('password');
    });

    it('should return "unknown" for users with no email or provider data', () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord({
        email: undefined,
        providerData: [],
      });

      // Act
      const provider = authAdapter.getPrimaryProvider(mockUserRecord);

      // Assert
      expect(provider).toBe('unknown');
    });
  });

  describe('validateUserRecord', () => {
    it('should validate a correct user record', () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord();

      // Act
      const validation = authAdapter.validateUserRecord(mockUserRecord);

      // Assert
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should reject user record without UID', () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord({
        uid: '',
      });

      // Act
      const validation = authAdapter.validateUserRecord(mockUserRecord);

      // Assert
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('User UID cannot be empty');
    });

    it('should reject user record with invalid email', () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord({
        email: 'invalid-email',
      });

      // Act
      const validation = authAdapter.validateUserRecord(mockUserRecord);

      // Assert
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Invalid email format');
    });

    it('should accept user record without email', () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord({
        email: undefined,
      });

      // Act
      const validation = authAdapter.validateUserRecord(mockUserRecord);

      // Assert
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });
  });

  describe('isTestUser', () => {
    it('should identify test users by email domain', () => {
      // Arrange
      const testCases = [
        '<EMAIL>',
        '<EMAIL>',
        'dev@localhost',
      ];

      testCases.forEach(email => {
        const mockUserRecord = global.testUtils.createMockUserRecord({ email });

        // Act
        const isTest = authAdapter.isTestUser(mockUserRecord);

        // Assert
        expect(isTest).toBe(true);
      });
    });

    it('should identify test users by email pattern', () => {
      // Arrange
      const testCases = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      testCases.forEach(email => {
        const mockUserRecord = global.testUtils.createMockUserRecord({ email });

        // Act
        const isTest = authAdapter.isTestUser(mockUserRecord);

        // Assert
        expect(isTest).toBe(true);
      });
    });

    it('should identify test users by display name', () => {
      // Arrange
      const testCases = [
        'Test User',
        'Dev Account',
        'Testing Profile',
      ];

      testCases.forEach(displayName => {
        const mockUserRecord = global.testUtils.createMockUserRecord({ displayName });

        // Act
        const isTest = authAdapter.isTestUser(mockUserRecord);

        // Assert
        expect(isTest).toBe(true);
      });
    });

    it('should not identify regular users as test users', () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord({
        email: '<EMAIL>',
        displayName: 'John Doe',
      });

      // Act
      const isTest = authAdapter.isTestUser(mockUserRecord);

      // Assert
      expect(isTest).toBe(false);
    });
  });

  describe('extractLoggingMetadata', () => {
    it('should extract comprehensive logging metadata', () => {
      // Arrange
      const customClaims = { role: 'admin' };
      const mockUserRecord = global.testUtils.createMockUserRecord({
        customClaims,
        providerData: [
          { providerId: 'google.com', uid: 'google-123' },
          { providerId: 'facebook.com', uid: 'fb-123' },
        ],
      });

      // Act
      const metadata = authAdapter.extractLoggingMetadata(mockUserRecord);

      // Assert
      expect(metadata).toEqual({
        uid: mockUserRecord.uid,
        email: mockUserRecord.email,
        emailVerified: mockUserRecord.emailVerified,
        disabled: mockUserRecord.disabled,
        providerCount: 2,
        primaryProvider: 'google.com',
        hasCustomClaims: true,
        creationTime: mockUserRecord.metadata.creationTime,
        lastSignInTime: mockUserRecord.metadata.lastSignInTime,
      });
    });

    it('should handle users without custom claims', () => {
      // Arrange
      const mockUserRecord = global.testUtils.createMockUserRecord({
        customClaims: {},
      });

      // Act
      const metadata = authAdapter.extractLoggingMetadata(mockUserRecord);

      // Assert
      expect(metadata.hasCustomClaims).toBe(false);
    });
  });
});
