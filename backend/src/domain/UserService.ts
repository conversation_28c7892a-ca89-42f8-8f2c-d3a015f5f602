/**
 * Core user lifecycle service implementing business logic
 * Orchestrates user profile creation and deletion with proper error handling
 */

import { UserRecord } from 'firebase-admin/auth';
import { FirestoreAdapter } from '../infra/FirestoreAdapter';
import { AuthAdapter } from '../infra/AuthAdapter';
import { LoggingService } from './LoggingService';
import { UserOperationResult, UserProfileConfig } from '../types/User';

export class UserService {
  constructor(
    private readonly firestoreAdapter: FirestoreAdapter,
    private readonly authAdapter: AuthAdapter,
    private readonly loggingService: LoggingService
  ) {}

  /**
   * Handle user creation event from Firebase Auth
   * Creates corresponding Firestore document with whitelisted auth data
   */
  async handleUserCreated(
    userRecord: UserRecord,
    config: UserProfileConfig = {
      includeCustomClaims: false,
      includeProviderData: true,
      additionalCollections: [],
    }
  ): Promise<UserOperationResult> {
    const uid = userRecord.uid;
    const primaryProvider = this.authAdapter.getPrimaryProvider(userRecord);
    
    try {
      // Validate user record
      const validation = this.authAdapter.validateUserRecord(userRecord);
      if (!validation.isValid) {
        const error = new Error(`Invalid user record: ${validation.errors.join(', ')}`);
        this.loggingService.logUserProfileCreationFailed(
          uid,
          error,
          primaryProvider,
          { validationErrors: validation.errors }
        );
        return {
          success: false,
          uid,
          error,
        };
      }

      // Extract user profile data
      const userProfile = this.authAdapter.extractUserProfile(
        userRecord,
        config.includeCustomClaims,
        config.includeProviderData
      );

      // Log debug information
      this.loggingService.logDebug(
        'user_profile_created',
        uid,
        'Starting user profile creation',
        {
          provider: primaryProvider,
          isTestUser: this.authAdapter.isTestUser(userRecord),
          config,
        }
      );

      // Create user profile in Firestore
      const result = await this.firestoreAdapter.createUserProfile(
        userProfile,
        config.additionalCollections
      );

      if (result.success) {
        // Log success
        this.loggingService.logUserProfileCreated(
          uid,
          primaryProvider,
          {
            ...result.metadata,
            userMetadata: this.authAdapter.extractLoggingMetadata(userRecord),
          }
        );
      } else {
        // Log failure
        this.loggingService.logUserProfileCreationFailed(
          uid,
          result.error!,
          primaryProvider,
          result.metadata
        );
      }

      return result;
    } catch (error) {
      const err = error as Error;
      this.loggingService.logUserProfileCreationFailed(
        uid,
        err,
        primaryProvider,
        { unexpectedError: true }
      );
      
      return {
        success: false,
        uid,
        error: err,
      };
    }
  }

  /**
   * Handle user deletion event from Firebase Auth
   * Removes corresponding Firestore document and sub-collections
   */
  async handleUserDeleted(
    uid: string,
    additionalCollections: string[] = []
  ): Promise<UserOperationResult> {
    try {
      // Log debug information
      this.loggingService.logDebug(
        'user_profile_deleted',
        uid,
        'Starting user profile deletion',
        { additionalCollections }
      );

      // Check if user profile exists before deletion
      const profileExists = await this.firestoreAdapter.userProfileExists(uid);
      
      if (!profileExists) {
        // Log warning but don't treat as error (idempotent deletion)
        this.loggingService.logUserDocumentNotFound(uid);
      }

      // Delete user profile from Firestore
      const result = await this.firestoreAdapter.deleteUserProfile(
        uid,
        additionalCollections
      );

      if (result.success) {
        // Log success
        this.loggingService.logUserProfileDeleted(uid, result.metadata);
      } else {
        // Log failure
        this.loggingService.logUserProfileDeletionFailed(
          uid,
          result.error!,
          result.metadata
        );
      }

      return result;
    } catch (error) {
      const err = error as Error;
      this.loggingService.logUserProfileDeletionFailed(
        uid,
        err,
        { unexpectedError: true }
      );
      
      return {
        success: false,
        uid,
        error: err,
      };
    }
  }

  /**
   * Validate user profile synchronization
   * Useful for health checks and data consistency verification
   */
  async validateUserProfileSync(uid: string): Promise<{
    isSync: boolean;
    authExists: boolean;
    firestoreExists: boolean;
    issues: string[];
  }> {
    const issues: string[] = [];
    
    try {
      // Check Firestore profile existence
      const firestoreExists = await this.firestoreAdapter.userProfileExists(uid);
      
      // Note: We can't easily check Auth existence from here without additional permissions
      // This would typically be called from a context where we know auth exists
      const authExists = true; // Assume true for now
      
      const isSync = authExists === firestoreExists;
      
      if (!isSync) {
        if (authExists && !firestoreExists) {
          issues.push('Auth user exists but Firestore profile is missing');
        } else if (!authExists && firestoreExists) {
          issues.push('Firestore profile exists but Auth user is missing');
        }
      }
      
      return {
        isSync,
        authExists,
        firestoreExists,
        issues,
      };
    } catch (error) {
      issues.push(`Validation failed: ${(error as Error).message}`);
      return {
        isSync: false,
        authExists: false,
        firestoreExists: false,
        issues,
      };
    }
  }
}
