/**
 * Structured logging service for Firebase Cloud Functions
 * Provides consistent, structured logging with proper severity levels
 */

import * as functions from 'firebase-functions';
import { LogEntry, LogEvent } from '../types/User';

export class LoggingService {
  /**
   * Create a structured log entry
   */
  private createLogEntry(
    severity: LogEntry['severity'],
    event: LogEvent,
    uid: string,
    options: {
      provider?: string;
      message?: string;
      error?: Error;
      metadata?: Record<string, any>;
    } = {}
  ): LogEntry {
    return {
      severity,
      event,
      uid,
      provider: options.provider,
      timestamp: new Date().toISOString(),
      message: options.message,
      error: options.error?.message,
      metadata: options.metadata,
    };
  }

  /**
   * Log user profile creation success
   */
  logUserProfileCreated(
    uid: string,
    provider: string,
    metadata?: Record<string, any>
  ): void {
    const logEntry = this.createLogEntry(
      'INFO',
      'user_profile_created',
      uid,
      {
        provider,
        message: `User profile created successfully for uid: ${uid}`,
        metadata,
      }
    );
    
    functions.logger.info('User profile created', logEntry);
  }

  /**
   * Log user profile deletion success
   */
  logUserProfileDeleted(
    uid: string,
    metadata?: Record<string, any>
  ): void {
    const logEntry = this.createLogEntry(
      'INFO',
      'user_profile_deleted',
      uid,
      {
        message: `User profile deleted successfully for uid: ${uid}`,
        metadata,
      }
    );
    
    functions.logger.info('User profile deleted', logEntry);
  }

  /**
   * Log user profile creation failure
   */
  logUserProfileCreationFailed(
    uid: string,
    error: Error,
    provider?: string,
    metadata?: Record<string, any>
  ): void {
    const logEntry = this.createLogEntry(
      'ERROR',
      'user_profile_creation_failed',
      uid,
      {
        provider,
        message: `Failed to create user profile for uid: ${uid}`,
        error,
        metadata,
      }
    );
    
    functions.logger.error('User profile creation failed', logEntry);
  }

  /**
   * Log user profile deletion failure
   */
  logUserProfileDeletionFailed(
    uid: string,
    error: Error,
    metadata?: Record<string, any>
  ): void {
    const logEntry = this.createLogEntry(
      'ERROR',
      'user_profile_deletion_failed',
      uid,
      {
        message: `Failed to delete user profile for uid: ${uid}`,
        error,
        metadata,
      }
    );
    
    functions.logger.error('User profile deletion failed', logEntry);
  }

  /**
   * Log warning for missing user document during deletion
   */
  logUserDocumentNotFound(uid: string): void {
    const logEntry = this.createLogEntry(
      'WARNING',
      'user_profile_deletion_failed',
      uid,
      {
        message: `User document not found during deletion for uid: ${uid}. This is expected if the user was already deleted.`,
      }
    );
    
    functions.logger.warn('User document not found during deletion', logEntry);
  }

  /**
   * Log debug information
   */
  logDebug(
    event: LogEvent,
    uid: string,
    message: string,
    metadata?: Record<string, any>
  ): void {
    const logEntry = this.createLogEntry(
      'DEBUG',
      event,
      uid,
      {
        message,
        metadata,
      }
    );
    
    functions.logger.debug(message, logEntry);
  }
}
