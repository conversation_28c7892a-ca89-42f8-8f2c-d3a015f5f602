{"functions": [{"source": "backend", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}], "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8080}, "ui": {"enabled": true}, "singleProjectMode": true}, "flutter": {"platforms": {"android": {"default": {"projectId": "bm-app-flutter-01", "appId": "1:14103996455:android:1623a450f5c93db8844602", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "bm-app-flutter-01", "appId": "1:14103996455:ios:2734347b471661f5844602", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "bm-app-flutter-01", "configurations": {"android": "1:14103996455:android:1623a450f5c93db8844602", "ios": "1:14103996455:ios:2734347b471661f5844602"}}}}}}