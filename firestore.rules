rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // User profile documents - only functions can write, users can read their own
    match /users/{userId} {
      // Users can read their own profile
      allow read: if request.auth != null && request.auth.uid == userId;
      
      // Only Cloud Functions can write to user profiles
      // This ensures data consistency and prevents client-side tampering
      allow write: if false; // Explicitly deny all client writes
      
      // Sub-collections under user documents
      match /{subcollection=**} {
        // Users can read their own sub-collections
        allow read: if request.auth != null && request.auth.uid == userId;
        
        // Only Cloud Functions can write to user sub-collections
        allow write: if false; // Explicitly deny all client writes
      }
    }
    
    // User settings collection - only functions can write, users can read their own
    match /userSettings/{userId} {
      // Users can read their own settings
      allow read: if request.auth != null && request.auth.uid == userId;
      
      // Only Cloud Functions can write to user settings
      allow write: if false; // Explicitly deny all client writes
    }
    
    // Example: Messages collection (from existing functions)
    // Users can read/write their own messages
    match /messages/{messageId} {
      allow read, write: if request.auth != null && 
                         request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
                    request.auth.uid == request.resource.data.userId;
    }
    
    // Admin-only collections (if needed in the future)
    match /admin/{document=**} {
      // Only allow access to users with admin custom claims
      allow read, write: if request.auth != null && 
                         request.auth.token.role == 'admin';
    }
    
    // Analytics and logging collections (read-only for authenticated users)
    match /analytics/{document=**} {
      allow read: if request.auth != null;
      allow write: if false; // Only functions can write analytics data
    }
    
    // Public data that anyone can read but only functions can write
    match /public/{document=**} {
      allow read: if true; // Anyone can read public data
      allow write: if false; // Only functions can write public data
    }
    
    // Default deny rule for any other collections
    match /{document=**} {
      allow read, write: if false;
    }
  }
}

// Additional security considerations:
// 
// 1. The rules above ensure that only Cloud Functions can create, update, or delete
//    user profile documents, maintaining strict data consistency.
//
// 2. Users can only read their own profile data, ensuring privacy.
//
// 3. All writes to user-related collections are blocked for clients, forcing
//    all mutations to go through the controlled Cloud Functions pipeline.
//
// 4. Sub-collections under user documents inherit the same security model.
//
// 5. The rules are designed to work with the user lifecycle functions that
//    automatically sync Firebase Auth with Firestore user profiles.
//
// 6. Future collections can be added following the same pattern of
//    function-only writes and user-specific reads.
//
// To deploy these rules:
// firebase deploy --only firestore:rules
//
// To test these rules locally:
// firebase emulators:start --only firestore
// Then use the Firestore emulator UI to test rule behavior
